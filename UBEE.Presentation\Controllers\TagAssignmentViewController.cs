using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using UBEE.MODELS.DataSource;
using UBEE.Shared.Views;
using UBEE.Shared.DataTransferObjects.Dto;

namespace UBEE.Presentation.Controllers;

[Route("api/tag-assignment-view")]
[ApiController]
public class TagAssignmentViewController : ControllerBase
{
    private readonly UBEEContext _context;

    public TagAssignmentViewController(UBEEContext context)
    {
        _context = context;
    }

    [HttpGet]
    public async Task<IActionResult> GetAll()
    {
        var results = await _context.vwTagAssignments.ToListAsync();
        return Ok(results);
    }

    [HttpGet("target/{targetId}")]
    public async Task<IActionResult> GetByTargetId(Guid targetId)
    {
        var results = await _context.vwTagAssignments
            .Where(v => v.TargetId == targetId)
            .ToListAsync();
        return Ok(results);
    }

    [HttpGet("target-type/{targetType}")]
    public async Task<IActionResult> GetByTargetType(string targetType)
    {
        var results = await _context.vwTagAssignments
            .Where(v => v.TargetType == targetType)
            .ToListAsync();
        return Ok(results);
    }

    [HttpGet("tag/{idTag}")]
    public async Task<IActionResult> GetByTagId(string idTag)
    {
        var results = await _context.vwTagAssignments
            .Where(v => v.IdTag == idTag)
            .ToListAsync();
        return Ok(results);
    }

    [HttpGet("target/{targetType}/{targetId}")]
    public async Task<IActionResult> GetByTargetTypeAndId(string targetType, Guid targetId)
    {
        var results = await _context.vwTagAssignments
            .Where(v => v.TargetType == targetType && v.TargetId == targetId)
            .ToListAsync();
        return Ok(results);
    }

    [HttpPost("search")]
    public async Task<IActionResult> Search([FromBody] PaginationDto payload)
    {
        int pageSize = payload.PageSize ?? 10;
        int pageNumber = payload.PageNumber ?? 1;
        int skip = (pageNumber - 1) * pageSize;
        string search = payload.SearchTerm?.ToLower() ?? "";

        var query = _context.vwTagAssignments.AsQueryable();

        // Apply search filter if provided
        if (!string.IsNullOrEmpty(search))
        {
            query = query.Where(v => 
                (v.TargetType != null && v.TargetType.ToLower().Contains(search)) ||
                (v.TargetName != null && v.TargetName.ToLower().Contains(search)) ||
                (v.Nom != null && v.Nom.ToLower().Contains(search)) ||
                (v.IdTag != null && v.IdTag.ToLower().Contains(search))
            );
        }

        var totalCount = await query.CountAsync();
        var results = await query
            .Skip(skip)
            .Take(pageSize)
            .ToListAsync();

        return Ok(new
        {
            Data = results,
            TotalCount = totalCount,
            PageNumber = pageNumber,
            PageSize = pageSize,
            TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
        });
    }
}
